import { BrowserWindow } from 'electron';
import { Application } from '@electron/main/core/application';
import { DIALOG_MARGIN_TOP, DIALOG_MARGIN } from '@electron/renderer/constants/design';
import { IBookmark } from '@electron/types';

export const showAddBookmarkDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
  data?: {
    url: string;
    title: string;
    bookmark?: IBookmark;
    favicon?: string;
  },
) => {
  console.log('[Main] showAddBookmarkDialog called with:', x, y, data);

  if (!data) {
    const selected = Application.instance.windows.current?.viewManager?.selected;
    if (!selected) {
      console.warn('[Main] No selected view available for bookmark dialog');
      return;
    }

    const {
      url,
      title,
      bookmark,
      favicon,
    } = selected;
    data = {
      url,
      title,
      bookmark,
      favicon,
    };
    console.log('[Main] Generated bookmark data:', data);
  }

  console.log('[Main] Creating bookmark dialog...');
  console.log('[Main] Application.instance:', !!Application.instance);
  console.log('[Main] Application.instance.dialogs:', !!Application.instance?.dialogs);

  const dialogWidth = 366;
  const dialogHeight = 240;

  // 弹窗左边缘与按钮左边缘对齐，向下显示
  let dialogX = x;
  let dialogY = y + DIALOG_MARGIN_TOP;

  // 边界检测 - 确保弹窗不会超出窗口右边界
  const windowBounds = browserWindow.getContentBounds();
  if (dialogX + dialogWidth > windowBounds.width) {
    dialogX = windowBounds.width - dialogWidth - DIALOG_MARGIN;
  }

  // 确保弹窗不会超出窗口左边界
  if (dialogX < DIALOG_MARGIN) {
    dialogX = DIALOG_MARGIN;
  }

  const bounds = {
    width: dialogWidth,
    height: dialogHeight,
    x: dialogX,
    y: dialogY,
  };

  console.log('[Main] Bookmark dialog bounds:', bounds);
  console.log('[Main] Input coordinates:', { x, y });
  console.log('[Main] Constants:', { DIALOG_MARGIN, DIALOG_MARGIN_TOP });

  const dialog = Application.instance.dialogs.show({
    name: 'add-bookmark',
    browserWindow,
    getBounds: () => bounds,
    onWindowBoundsUpdate: () => dialog.hide(),
  });

  console.log('[Main] Bookmark dialog created:', dialog ? 'success' : 'failed');

  if (!dialog) return;

  dialog.on('loaded', (e) => {
    console.log('[Main] Dialog loaded event, sending data:', data);
    e.reply('data', data);
  });
};
