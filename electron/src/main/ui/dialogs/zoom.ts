import { BrowserWindow } from 'electron';
import { Application } from '@electron/main/core/application';
import { DIALOG_MARGIN_TOP, DIALOG_MARGIN } from '@electron/renderer/constants/design';

export const showZoomDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
) => {
  const tabId = Application.instance.windows.fromBrowserWindow(browserWindow)
    .viewManager.selectedId;

  const dialog = Application.instance.dialogs.show({
    name: 'zoom',
    browserWindow,
    getBounds: () => {
      const dialogWidth = 280;
      const dialogHeight = 100;

      // 弹窗左边缘与按钮左边缘对齐，向下显示
      let dialogX = x;
      let dialogY = y + DIALOG_MARGIN_TOP;

      // 边界检测 - 确保弹窗不会超出窗口右边界
      const windowBounds = browserWindow.getContentBounds();
      if (dialogX + dialogWidth > windowBounds.width) {
        dialogX = windowBounds.width - dialogWidth - DIALOG_MARGIN;
      }

      // 确保弹窗不会超出窗口左边界
      if (dialogX < DIALOG_MARGIN) {
        dialogX = DIALOG_MARGIN;
      }

      return {
        width: dialogWidth,
        height: dialogHeight,
        x: dialogX,
        y: dialogY,
      };
    },
    onWindowBoundsUpdate: () => dialog.hide(),
  });

  if (!dialog) return;

  dialog.handle('tab-id', () => tabId);
};
