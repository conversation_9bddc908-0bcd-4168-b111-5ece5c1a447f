import { BrowserWindow } from 'electron';
import { Application } from '@electron/main/core/application';
import { DIALOG_MARGIN_TOP, DIALOG_MARGIN } from '@electron/renderer/constants/design';

export const showMenuDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
) => {
  console.log('[MenuDialog] Showing menu dialog at position:', { x, y });

  const menuWidth = 330;
  const menuHeight = 510;
  let isInitialShow = true;

  const dialog = Application.instance.dialogs.show({
    name: 'menu',
    browserWindow,
    getBounds: () => {
      // 获取窗口边界
      const windowBounds = browserWindow.getContentBounds();
      console.log('[MenuDialog] Window bounds:', windowBounds);

      // "更多"按钮特殊定位：右对齐，向左下方显示
      // x 是按钮的 right 坐标，让弹窗右边缘与按钮右边缘对齐
      let menuX = x - menuWidth;
      // 向下移动一点，增加与按钮的间距
      let menuY = y + 2;

      // 边界检测和调整
      // 检查左边界 - 优先检查左边界，因为弹窗现在是右对齐的
      if (menuX < DIALOG_MARGIN) {
        menuX = DIALOG_MARGIN;
        console.log('[MenuDialog] Adjusted X for left boundary:', menuX);
      }

      // 检查右边界
      if (menuX + menuWidth > windowBounds.width) {
        menuX = windowBounds.width - menuWidth - DIALOG_MARGIN;
        console.log('[MenuDialog] Adjusted X for right boundary:', menuX);
      }

      // 检查下边界
      if (menuY + menuHeight > windowBounds.height) {
        menuY = windowBounds.height - menuHeight - DIALOG_MARGIN;
        console.log('[MenuDialog] Adjusted Y for bottom boundary:', menuY);
      }

      // 检查上边界
      if (menuY < DIALOG_MARGIN) {
        menuY = DIALOG_MARGIN;
        console.log('[MenuDialog] Adjusted Y for top boundary:', menuY);
      }

      const finalBounds = {
        width: menuWidth,
        height: menuHeight,
        x: menuX,
        y: menuY,
      };

      console.log('[MenuDialog] Final bounds:', finalBounds);
      return finalBounds;
    },
    onWindowBoundsUpdate: (disposition) => {
      console.log('[MenuDialog] Window bounds updated:', disposition, 'isInitialShow:', isInitialShow);

      // 避免在初始显示时立即隐藏
      if (isInitialShow) {
        console.log('[MenuDialog] Ignoring initial bounds update');
        isInitialShow = false;
        return;
      }

      // 只在窗口移动或调整大小时隐藏菜单
      if (disposition === 'move' || disposition === 'resize') {
        console.log('[MenuDialog] Hiding dialog due to window', disposition);
        dialog.hide();
      }
    },
  });

  // 延迟重置初始显示标志，确保初始设置完成
  setTimeout(() => {
    isInitialShow = false;
  }, 100);

  console.log('[MenuDialog] Dialog creation result:', !!dialog);
};
