import {BrowserWindow} from 'electron';
import {Application} from '@electron/main/core/application';
import {DIALOG_MARGIN_TOP, DIALOG_MARGIN} from '@electron/renderer/constants/design';
import {IBookmark} from '@electron/types';

export const showBlockDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
  data: {
    data: [{
      url: string;
      filter: string;
    }],
    count: number
  },
) => {
  console.log('[Main] showBlockDialog called with:', x, y, data);
  console.log('[Main] Application.instance:', !!Application.instance);
  console.log('[Main] Application.instance.dialogs:', !!Application.instance?.dialogs);

  let dialog: any = null;

  dialog = Application.instance.dialogs.show({
    name: 'show-block',
    browserWindow,
    getBounds: () => {
      const dialogWidth = 466;
      const dialogHeight = 400;

      // 弹窗左边缘与按钮左边缘对齐，向下显示
      let dialogX = x;
      let dialogY = y + DIALOG_MARGIN_TOP;

      // 边界检测 - 确保弹窗不会超出窗口右边界
      const windowBounds = browserWindow.getContentBounds();
      if (dialogX + dialogWidth > windowBounds.width) {
        dialogX = windowBounds.width - dialogWidth - DIALOG_MARGIN;
      }

      // 确保弹窗不会超出窗口左边界
      if (dialogX < DIALOG_MARGIN) {
        dialogX = DIALOG_MARGIN;
      }

      return {
        width: dialogWidth,
        height: dialogHeight,
        x: dialogX,
        y: dialogY,
      };
    },
    onWindowBoundsUpdate: () => {
      // 现在可以安全地引用 dialog
      if (dialog) {
        dialog.hide();
      }
    },
  });

  console.log('[Main] Block dialog created:', dialog ? 'success' : 'failed');

  if (!dialog) return;

  dialog.on('loaded', (e) => {
    // 确保数据是可序列化的
    const serializedData = {
      data: data.data || [],
      count: data.count || 0
    };
    e.reply('data', serializedData);
  });
};
