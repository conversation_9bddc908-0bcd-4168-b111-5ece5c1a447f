import * as React from 'react';
import { createPortal } from 'react-dom';
import { cn } from '@browser/utils/tailwind-helpers';
import store from '@browser/views/app/store';

interface TooltipProps {
  children: React.ReactNode;
  content: string;
  delay?: number;
  position?: 'top' | 'bottom' | 'left' | 'right';
  disabled?: boolean;
}

export const Tooltip: React.FC<TooltipProps> = ({
  children,
  content,
  delay = 500,
  position = 'bottom',
  disabled = false
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [timeoutId, setTimeoutId] = React.useState<NodeJS.Timeout | null>(null);
  const [tooltipPosition, setTooltipPosition] = React.useState({ top: 0, left: 0 });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const triggerRef = React.useRef<HTMLDivElement>(null);

  const handleMouseEnter = () => {
    if (disabled || !content) return;

    const id = setTimeout(() => {
      // 计算 tooltip 位置
      if (triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 估算 tooltip 宽度（基于内容长度）
        const estimatedTooltipWidth = Math.min(content.length * 8 + 16, 300); // 最大宽度 300px
        const tooltipHeight = 32; // 估算高度

        let top = 0;
        let left = 0;

        switch (position) {
          case 'bottom':
            top = rect.bottom + 4;
            left = rect.left + rect.width / 2;

            // 添加调试信息
            console.log('[Tooltip] Element rect:', rect);
            console.log('[Tooltip] Estimated tooltip width:', estimatedTooltipWidth);
            console.log('[Tooltip] Viewport width:', viewportWidth);
            console.log('[Tooltip] Initial position:', { top, left });

            // 检查右边界溢出
            if (left + estimatedTooltipWidth / 2 > viewportWidth - 10) {
              left = viewportWidth - estimatedTooltipWidth - 10;
              console.log('[Tooltip] Adjusted for right boundary:', left);
            }
            // 检查左边界溢出
            if (left - estimatedTooltipWidth / 2 < 10) {
              left = estimatedTooltipWidth / 2 + 10;
              console.log('[Tooltip] Adjusted for left boundary:', left);
            }
            break;
          case 'top':
            top = rect.top - 4;
            left = rect.left + rect.width / 2;

            // 检查右边界溢出
            if (left + estimatedTooltipWidth / 2 > viewportWidth - 10) {
              left = viewportWidth - estimatedTooltipWidth - 10;
            }
            // 检查左边界溢出
            if (left - estimatedTooltipWidth / 2 < 10) {
              left = estimatedTooltipWidth / 2 + 10;
            }
            break;
          case 'left':
            top = rect.top + rect.height / 2;
            left = rect.left - 4;
            break;
          case 'right':
            top = rect.top + rect.height / 2;
            left = rect.right + 4;
            break;
        }

        setTooltipPosition({ top, left });
      }
      setIsVisible(true);
    }, delay);
    setTimeoutId(id);
  };

  const handleMouseLeave = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  React.useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  // 根据主题确定背景和文字颜色
  const isDarkTheme = store.theme['toolbar.lightForeground'];

  // Tooltip 容器样式 - 使用 fixed 定位
  const tooltipClasses = cn(
    'fixed px-2 py-1 text-xs rounded shadow-lg z-[99999]',
    'pointer-events-none transition-opacity duration-200',
    'max-w-[300px]', // 设置最大宽度
    // 根据内容长度决定是否换行
    content.length > 20 ? 'whitespace-normal' : 'whitespace-nowrap',
    isVisible ? 'opacity-100' : 'opacity-0',
    // 主题相关样式
    isDarkTheme
      ? 'text-white bg-gray-800 border border-gray-700'
      : 'text-gray-900 bg-white border border-gray-200'
  );

  // 计算 tooltip 的 transform 样式
  const getTooltipTransform = () => {
    if (!triggerRef.current) return 'translateX(-50%)';

    const rect = triggerRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const estimatedTooltipWidth = Math.min(content.length * 8 + 16, 300);

    // 特殊处理特定按钮
    const buttonElement = triggerRef.current.querySelector('[id]');
    const buttonId = buttonElement?.id || '';

    console.log('[Tooltip] Button ID:', buttonId, 'Content:', content);
    console.log('[Tooltip] Rect:', rect, 'ViewportWidth:', viewportWidth);

    // 特殊处理"更多"按钮 - 强制左对齐
    if (content === '更多' || buttonId === 'more-btn') {
      console.log('[Tooltip] Special handling for More button');
      return position === 'top' ? 'translateY(-100%)' : 'translateX(0)';
    }

    // 特殊处理"广告拦截"按钮 - 强制右对齐
    if (content.includes('广告拦截') || buttonId === 'blocker-btn') {
      console.log('[Tooltip] Special handling for AdBlock button');
      return position === 'top' ? 'translateX(-100%) translateY(-100%)' : 'translateX(-100%)';
    }

    switch (position) {
      case 'bottom':
      case 'top':
        // 检查是否需要调整水平对齐
        const centerX = rect.left + rect.width / 2;
        if (centerX + estimatedTooltipWidth / 2 > viewportWidth - 10) {
          // 右边界溢出，右对齐
          return position === 'top' ? 'translateX(-100%) translateY(-100%)' : 'translateX(-100%)';
        } else if (centerX - estimatedTooltipWidth / 2 < 10) {
          // 左边界溢出，左对齐
          return position === 'top' ? 'translateY(-100%)' : 'translateX(0)';
        } else {
          // 正常居中
          return position === 'top' ? 'translateX(-50%) translateY(-100%)' : 'translateX(-50%)';
        }
      case 'left':
        return 'translateX(-100%) translateY(-50%)';
      case 'right':
        return 'translateY(-50%)';
      default:
        return 'translateX(-50%)';
    }
  };

  // 计算箭头位置
  const getArrowPosition = () => {
    if (!triggerRef.current) return 'left-1/2 transform -translate-x-1/2';

    const rect = triggerRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const estimatedTooltipWidth = Math.min(content.length * 8 + 16, 300);
    const centerX = rect.left + rect.width / 2;

    if (position === 'bottom' || position === 'top') {
      if (centerX + estimatedTooltipWidth / 2 > viewportWidth - 10) {
        // 右边界溢出，箭头靠右
        return 'right-4';
      } else if (centerX - estimatedTooltipWidth / 2 < 10) {
        // 左边界溢出，箭头靠左
        return 'left-4';
      } else {
        // 正常居中
        return 'left-1/2 transform -translate-x-1/2';
      }
    }
    return 'left-1/2 transform -translate-x-1/2';
  };

  // 箭头样式 - 根据主题和位置调整
  const arrowClasses = cn(
    'absolute w-0 h-0',
    getArrowPosition(),
    // 根据主题和位置设置箭头颜色和位置
    position === 'bottom' && (isDarkTheme
      ? 'top-0 -translate-y-full border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-800'
      : 'top-0 -translate-y-full border-l-4 border-r-4 border-b-4 border-transparent border-b-white'),
    position === 'top' && (isDarkTheme
      ? 'bottom-0 translate-y-full border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800'
      : 'bottom-0 translate-y-full border-l-4 border-r-4 border-t-4 border-transparent border-t-white'),
    position === 'left' && (isDarkTheme
      ? 'right-0 top-1/2 transform translate-x-full -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-800'
      : 'right-0 top-1/2 transform translate-x-full -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-transparent border-l-white'),
    position === 'right' && (isDarkTheme
      ? 'left-0 top-1/2 transform -translate-x-full -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-800'
      : 'left-0 top-1/2 transform -translate-x-full -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-transparent border-r-white')
  );

  // 创建 tooltip 元素
  const tooltipElement = content && isVisible ? (
    <div
      ref={tooltipRef}
      className={tooltipClasses}
      style={{
        top: tooltipPosition.top,
        left: tooltipPosition.left,
        transform: getTooltipTransform(),
        visibility: isVisible ? 'visible' : 'hidden'
      }}
    >
      <div className={arrowClasses}></div>
      {content}
    </div>
  ) : null;

  return (
    <div
      ref={triggerRef}
      className="relative inline-block"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      {tooltipElement && createPortal(tooltipElement, document.body)}
    </div>
  );
};
